/**
 * Public API for sync operations
 * Provides clean interface for IPC handlers to interact with sync system
 */

import { EventEmitter } from 'events';
import * as path from 'path';
import * as fs from 'fs/promises';
import { unifiedSyncEngine } from './unified-sync-engine';
import { autoSync } from './auto-sync';
import { manifestManager } from './manifest-manager';
import { dbGet, dbAll } from '../../database/database-api';
import { getSetting, setSetting } from '../settings-api';
import {
  SyncResult,
  SyncOptions,
  SyncError,
  ErrorCode,
  SyncConfig,
  SyncState,
  SyncManifest
} from './types';

// Local interface to match the actual SyncProgress used in unified-sync-engine
interface SyncProgress {
  phase: string;
  progress: number;
  message: string;
}

interface SyncStatus {
  state: 'idle' | 'syncing' | 'error';
  lastSync: string | null;
  lastResult: 'success' | 'partial' | 'failed' | null;
  autoSyncEnabled: boolean;
  syncDirectory: string | null;
  nextScheduledSync: string | null;
  currentOperation: string | null;
  errors: string[];
}

interface SyncHistoryEntry {
  id: number;
  timestamp: string;
  result: 'success' | 'partial' | 'failed';
  itemsImported: number;
  itemsExported: number;
  conflicts: number;
  errors: string[];
  duration: number;
}

/**
 * Simple mutex implementation for sync operations
 */
class SyncMutex {
  private locked: boolean = false;
  private queue: (() => void)[] = [];

  async acquire(): Promise<void> {
    if (!this.locked) {
      this.locked = true;
      return;
    }

    // Wait for the lock to be released
    return new Promise<void>((resolve) => {
      this.queue.push(resolve);
    });
  }

  release(): void {
    if (this.queue.length > 0) {
      // Give lock to next in queue
      const next = this.queue.shift();
      next?.();
    } else {
      this.locked = false;
    }
  }
}

export class SyncAPI extends EventEmitter {
  private syncEngine = unifiedSyncEngine;
  private autoSyncInstance = autoSync;
  private currentStatus: SyncStatus;
  private isSyncing: boolean = false;
  private syncMutex = new SyncMutex();
  private pendingAutoSync: boolean = false;

  constructor() {
    super();
    this.currentStatus = {
      state: 'idle',
      lastSync: null,
      lastResult: null,
      autoSyncEnabled: false,
      syncDirectory: null,
      nextScheduledSync: null,
      currentOperation: null,
      errors: []
    };

    // Forward sync engine events
    this.syncEngine.on('progress', (progress: SyncProgress) => {
      this.emit('sync:progress', progress);
    });

    // Don't load settings in constructor - wait for database initialization
  }

  /**
   * Initialize sync API after database is ready
   */
  async initialize(): Promise<void> {
    await this.loadSettings();
  }

  /**
   * Load sync settings from database
   */
  private async loadSettings(): Promise<void> {
    try {
      const syncDirectory = await getSetting('syncDirectory');
      let autoSyncEnabled = await getSetting('autoSyncEnabled');
      const syncInterval = await getSetting('syncInterval');
      const debounceTime = await getSetting('syncDebounceTime');

      // Set auto-sync to true by default if not set
      if (!autoSyncEnabled) {
        await setSetting('autoSyncEnabled', true, 'sync');
        autoSyncEnabled = await getSetting('autoSyncEnabled');
      }

      if (syncDirectory?.value) {
        this.currentStatus.syncDirectory = syncDirectory.value;
      }

      // Enable auto-sync by default if sync directory is configured
      const shouldEnableAutoSync = autoSyncEnabled?.value !== false; // Default to true

      if (shouldEnableAutoSync && syncDirectory?.value) {
        const config: SyncConfig = {
          syncPath: syncDirectory.value,
          autoSync: true,
          syncInterval: syncInterval?.value || 30,
          conflictStrategy: 'ask',
          syncDeletions: true
        };
        
        await this.configure(config);
        this.currentStatus.autoSyncEnabled = true;
      } else {
        this.currentStatus.autoSyncEnabled = shouldEnableAutoSync;
      }
    } catch (error) {
      console.error('Error loading sync settings:', error);
    }
  }

  /**
   * Perform manual or auto sync
   */
  async performSync(directory: string, mode: 'manual' | 'auto' = 'manual'): Promise<SyncResult> {
    // Use mutex to ensure only one sync operation at a time
    await this.syncMutex.acquire();

    let result: SyncResult;

    try {
      // Double-check sync state after acquiring mutex
      if (this.isSyncing) {
        throw new SyncError(ErrorCode.SYNC_ERROR, 'Sync operation already in progress');
      }

      this.isSyncing = true;
      this.currentStatus.state = 'syncing';
      this.currentStatus.currentOperation = `${mode} sync`;
      this.currentStatus.errors = [];

      const startTime = Date.now();
      
      // Validate directory first
      await this.validateDirectory(directory);

      // Perform sync
      const engineResult = await this.syncEngine.sync(directory);
      
      // Map engine result to expected format
      result = {
        success: engineResult.success,
        imported: {
          books: 0,
          folders: 0,
          notes: 0
        },
        exported: {
          books: 0,
          folders: 0,
          notes: 0
        },
        deleted: {
          books: 0,
          folders: 0,
          notes: 0
        },
        conflicts: engineResult.conflicts || [],
        errors: engineResult.errors || [],
        timestamp: new Date().toISOString()
      };
      
      // TODO: Update when engine provides detailed counts
      result.imported.books = engineResult.itemsImported || 0;
      result.exported.books = engineResult.itemsExported || 0;

      // Update status
      this.currentStatus.lastSync = new Date().toISOString();
      this.currentStatus.lastResult = result.success ? 'success' : 
        result.errors.length > 0 ? 'failed' : 'partial';
      
      if (result.errors.length > 0) {
        this.currentStatus.errors = result.errors;
      }

      // Store sync history
      await this.storeSyncHistory({
        timestamp: new Date().toISOString(),
        result: this.currentStatus.lastResult,
        itemsImported: result.imported.books + result.imported.folders + result.imported.notes,
        itemsExported: result.exported.books + result.exported.folders + result.exported.notes,
        conflicts: result.conflicts.length,
        errors: result.errors,
        duration: Date.now() - startTime
      });

      // Update lastBackupTime setting for both manual and auto syncs
      const syncTimestamp = new Date().toISOString();
      await setSetting('lastBackupTime', syncTimestamp, 'backup');

      // Update next scheduled sync if auto-sync enabled
      if (this.autoSyncInstance && mode === 'auto') {
        const syncInterval = await getSetting('syncInterval');
        const intervalMinutes = syncInterval?.value || 30;
        const nextSync = new Date(Date.now() + intervalMinutes * 60 * 1000);
        this.currentStatus.nextScheduledSync = nextSync.toISOString();
      }

      this.emit('sync:complete', result);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.currentStatus.errors = [errorMessage];
      this.currentStatus.lastResult = 'failed';

      result = {
        success: false,
        imported: { books: 0, folders: 0, notes: 0 },
        exported: { books: 0, folders: 0, notes: 0 },
        deleted: { books: 0, folders: 0, notes: 0 },
        conflicts: [],
        errors: [errorMessage],
        timestamp: new Date().toISOString()
      };

      this.emit('sync:error', error);
      throw error;
    } finally {
      this.isSyncing = false;
      this.currentStatus.state = 'idle';
      this.currentStatus.currentOperation = null;
      // Release the mutex to allow next sync operation
      this.syncMutex.release();
      
      // If there was a pending auto-sync, process it now
      if (this.pendingAutoSync && this.currentStatus.syncDirectory) {
        this.pendingAutoSync = false;
        // Don't await this - let it run independently
        this.performSync(this.currentStatus.syncDirectory, 'auto').catch(error => {
          console.error('Pending auto-sync failed:', error);
        });
      }
    }
  }

  /**
   * Import backup from external directory
   */
  async importBackup(directory: string): Promise<SyncResult> {
    if (this.isSyncing) {
      throw new SyncError(ErrorCode.SYNC_ERROR, 'Sync operation already in progress');
    }

    this.isSyncing = true;
    this.currentStatus.state = 'syncing';
    this.currentStatus.currentOperation = 'import backup';
    
    try {
      // Validate directory
      await this.validateDirectory(directory);

      // Check for manifest
      const manifest = await manifestManager.loadManifest(directory);
      if (!manifest) {
        throw new SyncError(ErrorCode.PATH_ERROR, 'No valid backup found in directory');
      }

      // Perform import with force option
      const options: SyncOptions = {
        force: true,
        conflictResolution: 'remote' // Prefer backup data
      };

      const engineResult = await this.syncEngine.sync(directory);
      
      // Map engine result to expected format
      const result: SyncResult = {
        success: engineResult.success,
        imported: {
          books: engineResult.itemsImported || 0,
          folders: 0,
          notes: 0
        },
        exported: {
          books: 0,
          folders: 0,
          notes: 0
        },
        deleted: {
          books: 0,
          folders: 0,
          notes: 0
        },
        conflicts: engineResult.conflicts || [],
        errors: engineResult.errors || [],
        timestamp: new Date().toISOString()
      };
      
      this.emit('import:complete', result);
      return result;

    } catch (error) {
      this.emit('import:error', error);
      throw error;

    } finally {
      this.isSyncing = false;
      this.currentStatus.state = 'idle';
      this.currentStatus.currentOperation = null;
    }
  }

  /**
   * Get current sync status
   */
  async getStatus(): Promise<SyncStatus> {
    return { ...this.currentStatus };
  }

  /**
   * Configure sync settings
   */
  async configure(settings: Partial<SyncConfig>): Promise<void> {
    try {
      // Update sync directory
      if (settings.syncPath !== undefined) {
        if (settings.syncPath) {
          await this.validateDirectory(settings.syncPath);

          // CRITICAL FIX: Initialize backup location when sync path is set
          // This ensures manifest is created with current database state
          await manifestManager.initializeBackupLocation(settings.syncPath);
        }
        await setSetting('syncDirectory', settings.syncPath, 'sync');
        this.currentStatus.syncDirectory = settings.syncPath;
      }

      // Update auto-sync
      if (settings.autoSync !== undefined) {
        await setSetting('autoSyncEnabled', settings.autoSync, 'sync');
        this.currentStatus.autoSyncEnabled = settings.autoSync;

        if (settings.autoSync && this.currentStatus.syncDirectory) {
          // Enable auto-sync
          if (!this.autoSyncInstance.isRunning()) {
            // Set up sync handler if not already set
            this.autoSyncInstance.removeAllListeners('sync-start');
            this.autoSyncInstance.on('sync-start', async () => {
              console.log('[SyncAPI] Received sync-start event from auto-sync');
              try {
                const result = await this.performSync(this.currentStatus.syncDirectory!, 'auto');
                // Notify auto-sync of completion
                this.autoSyncInstance.onSyncComplete(result);
              } catch (error) {
                // Check if the error is due to a sync already in progress
                if (error instanceof SyncError && error.message.includes('already in progress')) {
                  // Set flag to perform sync after current one completes
                  this.pendingAutoSync = true;
                  console.log('Auto-sync deferred - sync already in progress');
                  // Reset auto-sync state since we couldn't start
                  this.autoSyncInstance.onSyncError(new Error('Sync already in progress'));
                } else {
                  console.error('Auto-sync failed:', error);
                  // Notify auto-sync of error
                  this.autoSyncInstance.onSyncError(error as Error);
                }
              }
            });

            await this.autoSyncInstance.start(this.currentStatus.syncDirectory, {
              enabled: true,
              debounceTime: 5000,
              syncInterval: (settings.syncInterval || 30) * 60 * 1000
            });
          }
        } else if (this.autoSyncInstance.isRunning()) {
          // Disable auto-sync
          this.autoSyncInstance.stop();
          this.currentStatus.nextScheduledSync = null;
        }
      }

      // Update sync interval
      if (settings.syncInterval !== undefined) {
        await setSetting('syncInterval', settings.syncInterval, 'sync');
        
        if (this.autoSyncInstance.isRunning() && this.currentStatus.syncDirectory) {
          // Restart auto-sync with new interval
          this.autoSyncInstance.stop();
          await this.autoSyncInstance.start(this.currentStatus.syncDirectory, {
            enabled: true,
            debounceTime: 5000,
            syncInterval: settings.syncInterval * 60 * 1000
          });
        }
      }

      // Update conflict strategy
      if (settings.conflictStrategy !== undefined) {
        await setSetting('syncConflictStrategy', settings.conflictStrategy, 'sync');
      }

      // Update sync deletions
      if (settings.syncDeletions !== undefined) {
        await setSetting('syncDeletions', settings.syncDeletions, 'sync');
      }

      this.emit('config:updated', settings);

    } catch (error) {
      console.error('Error configuring sync:', error);
      throw error;
    }
  }

  /**
   * Get sync history from database
   */
  async getSyncHistory(directory?: string): Promise<SyncHistoryEntry[]> {
    try {
      // For now, return empty array as we don't have sync_state table yet
      // This will be implemented when database schema is updated
      return [];
    } catch (error) {
      console.error('Error getting sync history:', error);
      throw error;
    }
  }

  /**
   * Clear sync state for fresh start
   */
  async clearSyncState(directory: string): Promise<void> {
    try {
      // Clear manifest
      const defaultManifest = await manifestManager.createDefaultManifest();
      await manifestManager.saveManifest(directory, defaultManifest);
      
      // Clear sync history from database (when implemented)
      // await dbRun('DELETE FROM sync_state WHERE sync_directory = ?', [directory]);
      
      // Reset status
      if (this.currentStatus.syncDirectory === directory) {
        this.currentStatus.lastSync = null;
        this.currentStatus.lastResult = null;
        this.currentStatus.errors = [];
      }

      this.emit('state:cleared', directory);

    } catch (error) {
      console.error('Error clearing sync state:', error);
      throw error;
    }
  }

  /**
   * Validate directory for sync operations
   */
  async validateDirectory(directory: string): Promise<void> {
    if (!directory) {
      throw new SyncError(ErrorCode.PATH_ERROR, 'Directory path is required');
    }

    try {
      const stats = await fs.stat(directory);
      if (!stats.isDirectory()) {
        throw new SyncError(ErrorCode.PATH_ERROR, 'Path is not a directory');
      }

      // Check read/write permissions
      await fs.access(directory, fs.constants.R_OK | fs.constants.W_OK);

    } catch (error) {
      if (error instanceof SyncError) {
        throw error;
      }
      
      if ((error as any).code === 'ENOENT') {
        // Try to create directory
        try {
          await fs.mkdir(directory, { recursive: true });
        } catch (mkdirError) {
          throw new SyncError(ErrorCode.PATH_ERROR, 'Cannot create sync directory');
        }
      } else if ((error as any).code === 'EACCES') {
        throw new SyncError(ErrorCode.PATH_ERROR, 'No permission to access directory');
      } else {
        throw new SyncError(ErrorCode.PATH_ERROR, 'Invalid directory path');
      }
    }
  }

  /**
   * Store sync history entry (placeholder for future implementation)
   */
  private async storeSyncHistory(entry: Omit<SyncHistoryEntry, 'id'>): Promise<void> {
    try {
      // This will be implemented when sync_state table is added to database
      // For now, this is a placeholder method
    } catch (error) {
      console.error('Error storing sync history:', error);
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.autoSyncInstance) {
      this.autoSyncInstance.stop();
    }
    
    this.removeAllListeners();
  }
}

// Export singleton instance
export const syncAPI = new SyncAPI();