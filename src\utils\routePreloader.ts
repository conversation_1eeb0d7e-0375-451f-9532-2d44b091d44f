/**
 * Route Preloader - Intelligent preloading of Vue components and data
 * 
 * Preloads components during app startup and idle time to reduce navigation delays
 */

import type { Router } from 'vue-router'

interface PreloadConfig {
  enabled: boolean
  delayAfterStartup: number
  priorityRoutes: string[]
  backgroundRoutes: string[]
  preloadData: boolean
}

interface PreloadResult {
  route: string
  success: boolean
  loadTime: number
  error?: string
}

class RoutePreloader {
  private config: PreloadConfig = {
    enabled: true,
    delayAfterStartup: 1500, // Wait 1.5s after app startup
    priorityRoutes: ['Dashboard', 'Notes'], // Load these first
    backgroundRoutes: ['Books', 'Folders', 'Timer', 'Settings'], // Load these after
    preloadData: true
  }

  private router: Router | null = null
  private preloadedRoutes = new Set<string>()
  private preloadResults: PreloadResult[] = []
  private isPreloading = false

  /**
   * Initialize the preloader with router instance
   */
  public initialize(router: Router, customConfig?: Partial<PreloadConfig>): void {
    this.router = router
    
    if (customConfig) {
      this.config = { ...this.config, ...customConfig }
    }

    if (this.config.enabled) {
      this.startPreloading()
    }

    console.log('🚀 Route preloader initialized', {
      enabled: this.config.enabled,
      delayAfterStartup: this.config.delayAfterStartup,
      priorityRoutes: this.config.priorityRoutes,
      backgroundRoutes: this.config.backgroundRoutes
    })
  }

  /**
   * Start the preloading process
   */
  private async startPreloading(): Promise<void> {
    if (this.isPreloading || !this.router) return

    this.isPreloading = true
    console.log(`⏳ Starting route preloading in ${this.config.delayAfterStartup}ms...`)

    // Wait for app to fully initialize
    await this.delay(this.config.delayAfterStartup)

    try {
      // Phase 1: Preload priority routes (critical for UX)
      console.log('🎯 Phase 1: Preloading priority routes...')
      await this.preloadRoutesBatch(this.config.priorityRoutes, 'priority')

      // Phase 2: Preload background routes (nice to have)
      console.log('🔄 Phase 2: Preloading background routes...')
      await this.preloadRoutesBatch(this.config.backgroundRoutes, 'background')

      console.log('✅ Route preloading completed!', {
        totalPreloaded: this.preloadedRoutes.size,
        results: this.preloadResults
      })

    } catch (error) {
      console.error('❌ Route preloading failed:', error)
    } finally {
      this.isPreloading = false
    }
  }

  /**
   * Preload a batch of routes
   */
  private async preloadRoutesBatch(routeNames: string[], phase: string): Promise<void> {
    const startTime = performance.now()
    
    for (const routeName of routeNames) {
      if (this.preloadedRoutes.has(routeName)) {
        console.log(`⏭️ Skipping ${routeName} (already preloaded)`)
        continue
      }

      try {
        const loadTime = await this.preloadRoute(routeName)
        this.preloadResults.push({
          route: routeName,
          success: true,
          loadTime
        })
        
        console.log(`✅ Preloaded ${routeName} in ${loadTime.toFixed(2)}ms`)
        
        // Small delay between preloads to avoid blocking the main thread
        if (phase === 'background') {
          await this.delay(100)
        }
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        this.preloadResults.push({
          route: routeName,
          success: false,
          loadTime: 0,
          error: errorMessage
        })
        
        console.warn(`⚠️ Failed to preload ${routeName}:`, errorMessage)
      }
    }

    const totalTime = performance.now() - startTime
    console.log(`📊 ${phase} preloading completed in ${totalTime.toFixed(2)}ms`)
  }

  /**
   * Preload a single route component
   */
  private async preloadRoute(routeName: string): Promise<number> {
    if (!this.router) throw new Error('Router not initialized')

    const route = this.router.getRoutes().find(r => r.name === routeName)
    if (!route) throw new Error(`Route ${routeName} not found`)

    const startTime = performance.now()

    // Preload the component
    if (typeof route.component === 'function') {
      await route.component()
    }

    // Preload route data if enabled
    if (this.config.preloadData) {
      await this.preloadRouteData(routeName)
    }

    // Mark as preloaded
    this.preloadedRoutes.add(routeName)

    return performance.now() - startTime
  }

  /**
   * Preload data for specific routes (optional enhancement)
   */
  private async preloadRouteData(routeName: string): Promise<void> {
    if (!this.config.preloadData) return

    try {
      // Import the electron API for data preloading
      const { useElectronAPI } = await import('../useElectronAPI')
      const db = useElectronAPI()

      console.log(`📊 Preloading data for ${routeName}...`)
      const startTime = performance.now()

      switch (routeName) {
        case 'Notes':
          // Preload notes and folders data
          await Promise.all([
            db.notes.getAll(),
            db.folders.getAll()
          ])
          break

        case 'Books':
          // Preload books data
          await db.books.getBooksWithMetadata()
          break

        case 'Folders':
          // Preload folders and notes data
          await Promise.all([
            db.folders.getAll(),
            db.notes.getAll()
          ])
          break

        case 'Timer':
          // Preload timer sessions and stats
          const thirtyDaysAgo = new Date()
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
          const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0]
          const todayStr = new Date().toISOString().split('T')[0]

          await db.timer.getSessionsByDateRange(thirtyDaysAgoStr, todayStr)
          break

        case 'Dashboard':
          // Preload dashboard data (notes, books, timer stats)
          await Promise.all([
            db.notes.getAll(),
            db.books.getAll(),
            db.timer.getSessionsByDateRange(
              new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              new Date().toISOString().split('T')[0]
            )
          ])
          break

        default:
          // No specific data preloading for this route
          break
      }

      const loadTime = performance.now() - startTime
      console.log(`✅ Data preloaded for ${routeName} in ${loadTime.toFixed(2)}ms`)

    } catch (error) {
      console.warn(`⚠️ Failed to preload data for ${routeName}:`, error)
    }
  }

  /**
   * Check if a route is already preloaded
   */
  public isRoutePreloaded(routeName: string): boolean {
    return this.preloadedRoutes.has(routeName)
  }

  /**
   * Get preloading statistics
   */
  public getStats(): {
    totalPreloaded: number
    preloadedRoutes: string[]
    results: PreloadResult[]
    averageLoadTime: number
  } {
    const successfulResults = this.preloadResults.filter(r => r.success)
    const averageLoadTime = successfulResults.length > 0
      ? successfulResults.reduce((sum, r) => sum + r.loadTime, 0) / successfulResults.length
      : 0

    return {
      totalPreloaded: this.preloadedRoutes.size,
      preloadedRoutes: Array.from(this.preloadedRoutes),
      results: this.preloadResults,
      averageLoadTime
    }
  }

  /**
   * Enable/disable preloading
   */
  public setEnabled(enabled: boolean): void {
    this.config.enabled = enabled
    console.log(`🔧 Route preloading ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<PreloadConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('🔧 Route preloader config updated:', this.config)
  }

  /**
   * Manually trigger preloading of specific routes
   */
  public async preloadSpecificRoutes(routeNames: string[]): Promise<void> {
    if (!this.router) {
      console.warn('Router not initialized, cannot preload routes')
      return
    }

    console.log('🎯 Manual preloading triggered for:', routeNames)
    await this.preloadRoutesBatch(routeNames, 'manual')
  }

  /**
   * Clear preloaded routes (for testing)
   */
  public clearPreloaded(): void {
    this.preloadedRoutes.clear()
    this.preloadResults = []
    console.log('🗑️ Cleared preloaded routes')
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Export singleton instance
export const routePreloader = new RoutePreloader()

// Export types for external use
export type { PreloadConfig, PreloadResult }
